﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class DebtPaymentManager : IDebtPaymentService
    {
        private readonly IDebtPaymentDal _debtPaymentDal;

        public DebtPaymentManager(IDebtPaymentDal debtPaymentDal)
        {
            _debtPaymentDal = debtPaymentDal;
        }
        [SecuredOperation("owner,admin")]
        [CacheInvalidationAspect("DebtPayment", "RemainingDebt", "Payment")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int debtPaymentId)
        {
            return _debtPaymentDal.DeleteDebtPaymentWithRemainingDebtUpdate(debtPaymentId);
        }
    }
}
