using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UnifiedCompanyManager : IUnifiedCompanyService
    {
        private readonly IUnifiedCompanyDal _unifiedCompanyDal;

        public UnifiedCompanyManager(IUnifiedCompanyDal unifiedCompanyDal)
        {
            _unifiedCompanyDal = unifiedCompanyDal;
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(5)]
        [CacheInvalidationAspect("Company", "User")]
        public IResult AddUnifiedCompany(UnifiedCompanyAddDto unifiedCompanyDto)
        {
            // SOLID prensiplerine uygun: Complex multi-entity creation işlemini DAL katmanına taşıdık
            var result = _unifiedCompanyDal.AddUnifiedCompanyWithAllEntities(unifiedCompanyDto);

            if (result.Success)
            {
                return new SuccessResult(result.Message);
            }

            return new ErrorResult(result.Message);
        }
    }
}
