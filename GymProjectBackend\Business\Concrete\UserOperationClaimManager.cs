﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;

namespace Business.Concrete
{
    public class UserOperationClaimManager : IUserOperationClaimService
    {
        private readonly IUserOperationClaimDal _userOperationClaimDal;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserOperationClaimManager(
            IUserOperationClaimDal userOperationClaimDal,
            IHttpContextAccessor httpContextAccessor)
        {
            _userOperationClaimDal = userOperationClaimDal;
            _httpContextAccessor = httpContextAccessor;
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 600)] // 10 dakika - Dashboard verisi
        public IDataResult<List<UserOperationClaimDto>> GetUserOperationClaimDetails()
        {
            return new SuccessDataResult<List<UserOperationClaimDto>>(_userOperationClaimDal.GetUserOperationClaimDetails());
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [CacheInvalidationAspect("UserOperationClaim", "User")]
        public IResult Add(UserOperationClaim userOperationClaim)
        {
            _userOperationClaimDal.Add(userOperationClaim);
            InvalidateUserClaims(userOperationClaim.UserId);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [CacheInvalidationAspect("UserOperationClaim", "User")]
        public IResult Delete(int id)
        {
            var claim = _userOperationClaimDal.Get(x => x.UserOperationClaimId == id);
            if (claim != null)
            {
                _userOperationClaimDal.HardDelete(id);
                InvalidateUserClaims(claim.UserId);
            }
            return new SuccessResult(Messages.UserOperationClaimDeleted);
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 900)] // 15 dakika - Temel liste
        public IDataResult<List<UserOperationClaim>> GetAll()
        {
            return new SuccessDataResult<List<UserOperationClaim>>(_userOperationClaimDal.GetAll(u=>u.IsActive==true), Messages.UserOperationClaimsListed);
        }

        [SecuredOperation("owner")]
        [CacheAspect(duration: 1800)] // 30 dakika - Detay verisi
        public IDataResult<UserOperationClaim> GetById(int id)
        {
            return new SuccessDataResult<UserOperationClaim>(_userOperationClaimDal.Get(u => u.UserOperationClaimId == id));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [CacheInvalidationAspect("UserOperationClaim", "User")]
        public IResult Update(UserOperationClaim userOperationClaim)
        {
            _userOperationClaimDal.Update(userOperationClaim);
            InvalidateUserClaims(userOperationClaim.UserId);
            return new SuccessResult(Messages.UserOperationClaimUpdated);
        }

        public IResult InvalidateUserClaims(int userId)
        {
            // SOLID prensiplerine uygun: Complex token invalidation logic'ini DAL'a taşıdık
            return _userOperationClaimDal.InvalidateUserTokensByUserId(userId);
        }

        // Güvenlik kontrolü olmadan üye kaydı için özel metot
        [LogAspect]
        [CacheInvalidationAspect("UserOperationClaim", "User")]
        public IResult AddForRegistration(UserOperationClaim userOperationClaim)
        {
            // IP adresi ve zaman damgası gibi bilgileri loglama
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? "Unknown";
            var timestamp = DateTime.Now;

            // Log mesajı
            Console.WriteLine($"[{timestamp}] AddForRegistration called: UserId={userOperationClaim.UserId}, RoleId={userOperationClaim.OperationClaimId}, IP: {ipAddress}");

            _userOperationClaimDal.Add(userOperationClaim);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }
    }
}
