using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache Aspect - Method'lara otomatik cache ekleme
    /// Multi-tenant yapıya uygun, performance optimized
    /// 
    /// Kullanım:
    /// [CacheAspect(duration: 300)] // 5 dakika
    /// [CacheAspect(duration: 1800, tags: "member-list")]
    /// [CacheAspect(duration: 600, condition: "user.Role == 'Admin'")]
    /// </summary>
    public class CacheAspect : MethodInterception
    {
        #region Fields

        private readonly int _duration;
        private readonly string[] _tags;
        private readonly string _condition;
        private readonly bool _enableLogging;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogger<CacheAspect> _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Cache Aspect Constructor
        /// </summary>
        /// <param name="duration">Cache süresi (saniye)</param>
        /// <param name="tags">Cache tag'leri (invalidation için)</param>
        /// <param name="condition">Cache koşulu (opsiyonel)</param>
        /// <param name="enableLogging">Logging aktif mi</param>
        public CacheAspect(int duration = 1800, string tags = "", string condition = "", bool enableLogging = true)
        {
            _duration = duration;
            _tags = string.IsNullOrWhiteSpace(tags) ? new string[0] : tags.Split(',').Select(t => t.Trim()).ToArray();
            _condition = condition;
            _enableLogging = enableLogging;

            // Dependency injection
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _logger = ServiceTool.ServiceProvider.GetService<ILogger<CacheAspect>>();
        }

        #endregion

        #region Method Interception

        public override void Intercept(IInvocation invocation)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                // Cache key oluştur
                var cacheKey = GenerateCacheKey(invocation);
                
                if (string.IsNullOrEmpty(cacheKey))
                {
                    // Cache key oluşturulamazsa method'u normal çalıştır
                    invocation.Proceed();
                    return;
                }

                // Cache'den veri al
                var cachedValue = _cacheService.Get<object>(cacheKey);
                
                if (cachedValue != null)
                {
                    // Cache HIT
                    invocation.ReturnValue = cachedValue;
                    
                    if (_enableLogging)
                    {
                        _logger.LogDebug("Cache HIT: {Method} - {Key} - {Duration}ms", 
                            GetMethodSignature(invocation), cacheKey, stopwatch.ElapsedMilliseconds);
                    }
                    
                    return;
                }

                // Cache MISS - Method'u çalıştır
                invocation.Proceed();
                
                // Sonucu cache'e kaydet
                if (invocation.ReturnValue != null)
                {
                    var expiry = TimeSpan.FromSeconds(_duration);
                    _cacheService.Set(cacheKey, invocation.ReturnValue, expiry);
                    
                    if (_enableLogging)
                    {
                        _logger.LogDebug("Cache MISS: {Method} - {Key} - Cached for {Duration}s - {ExecutionTime}ms", 
                            GetMethodSignature(invocation), cacheKey, _duration, stopwatch.ElapsedMilliseconds);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache aspect error for method: {Method}", GetMethodSignature(invocation));
                
                // Cache hatası durumunda method'u normal çalıştır
                if (invocation.ReturnValue == null)
                {
                    invocation.Proceed();
                }
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        #endregion

        #region Cache Key Generation

        /// <summary>
        /// Method ve parametrelerine göre cache key oluştur
        /// Pattern: gym:{companyId}:method:{className}:{methodName}:{paramHash}
        /// </summary>
        private string GenerateCacheKey(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID for cache key generation: {CompanyId}", companyId);
                    return null;
                }

                var className = invocation.TargetType.Name;
                var methodName = invocation.Method.Name;
                var paramHash = GenerateParameterHash(invocation);

                return $"gym:{companyId}:method:{className}:{methodName}:{paramHash}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating cache key for method: {Method}", GetMethodSignature(invocation));
                return null;
            }
        }

        /// <summary>
        /// Method parametrelerinden hash oluştur
        /// </summary>
        private string GenerateParameterHash(IInvocation invocation)
        {
            try
            {
                if (invocation.Arguments == null || invocation.Arguments.Length == 0)
                {
                    return "noparams";
                }

                var parameterData = new List<object>();
                
                for (int i = 0; i < invocation.Arguments.Length; i++)
                {
                    var arg = invocation.Arguments[i];
                    var paramName = invocation.Method.GetParameters()[i].Name;
                    
                    parameterData.Add(new { Name = paramName, Value = SerializeParameter(arg) });
                }

                var json = JsonConvert.SerializeObject(parameterData, Formatting.None);
                return GenerateHash(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating parameter hash");
                return "hasherror";
            }
        }

        /// <summary>
        /// Parametreyi serialize et (güvenli)
        /// </summary>
        private object SerializeParameter(object parameter)
        {
            if (parameter == null) return null;
            
            var type = parameter.GetType();
            
            // Primitive types
            if (type.IsPrimitive || type == typeof(string) || type == typeof(DateTime) || type == typeof(decimal))
            {
                return parameter;
            }
            
            // Complex types - sadece ID'yi al (performance için)
            var idProperty = type.GetProperty("Id") ?? type.GetProperty("ID") ?? 
                           type.GetProperties().FirstOrDefault(p => p.Name.EndsWith("Id") || p.Name.EndsWith("ID"));
            
            if (idProperty != null)
            {
                return idProperty.GetValue(parameter);
            }
            
            // Fallback - ToString
            return parameter.ToString();
        }

        /// <summary>
        /// String'den MD5 hash oluştur
        /// </summary>
        private string GenerateHash(string input)
        {
            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes).ToLower();
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Method signature'ını al (logging için)
        /// </summary>
        private string GetMethodSignature(IInvocation invocation)
        {
            return $"{invocation.TargetType.Name}.{invocation.Method.Name}";
        }

        #endregion
    }
}
