using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache Invalidation Aspect - Entity değişikliklerinde ilgili cache'leri temizle
    /// Multi-tenant yapıya uygun, pattern-based invalidation
    /// 
    /// Kullanım:
    /// [CacheInvalidationAspect("Member")]
    /// [CacheInvalidationAspect("Member", "Payment")]
    /// [CacheInvalidationAspect("Member", invalidateMethod: true)]
    /// </summary>
    public class CacheInvalidationAspect : MethodInterception
    {
        #region Fields

        private readonly string[] _entityNames;
        private readonly bool _invalidateMethod;
        private readonly bool _invalidateList;
        private readonly bool _invalidateSearch;
        private readonly bool _enableLogging;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly ILogger<CacheInvalidationAspect> _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Cache Invalidation Aspect Constructor
        /// </summary>
        /// <param name="entityNames">Temizlenecek entity isimleri</param>
        /// <param name="invalidateMethod">Method cache'lerini temizle</param>
        /// <param name="invalidateList">Liste cache'lerini temizle</param>
        /// <param name="invalidateSearch">Arama cache'lerini temizle</param>
        /// <param name="enableLogging">Logging aktif mi</param>
        public CacheInvalidationAspect(params string[] entityNames)
            : this(true, true, true, true, entityNames)
        {
        }

        public CacheInvalidationAspect(bool invalidateMethod, bool invalidateList, bool invalidateSearch, bool enableLogging, params string[] entityNames)
        {
            _entityNames = entityNames ?? new string[0];
            _invalidateMethod = invalidateMethod;
            _invalidateList = invalidateList;
            _invalidateSearch = invalidateSearch;
            _enableLogging = enableLogging;

            // Dependency injection
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _logger = ServiceTool.ServiceProvider.GetService<ILogger<CacheInvalidationAspect>>();
        }

        #endregion

        #region Method Interception

        protected override void OnAfter(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID for cache invalidation: {CompanyId}", companyId);
                    return;
                }

                var invalidatedCount = 0;

                foreach (var entityName in _entityNames)
                {
                    invalidatedCount += InvalidateEntityCache(companyId, entityName);
                }

                if (_enableLogging && invalidatedCount > 0)
                {
                    _logger.LogDebug("Cache invalidation completed: {Method} - {Count} keys invalidated for entities: {Entities}", 
                        GetMethodSignature(invocation), invalidatedCount, string.Join(", ", _entityNames));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache invalidation error for method: {Method}", GetMethodSignature(invocation));
            }
        }

        #endregion

        #region Cache Invalidation Logic

        /// <summary>
        /// Belirli entity'nin cache'lerini temizle
        /// </summary>
        private int InvalidateEntityCache(int companyId, string entityName)
        {
            var invalidatedCount = 0;

            try
            {
                // Method cache'lerini temizle
                if (_invalidateMethod)
                {
                    var methodPattern = $"gym:{companyId}:method:*{entityName}*";
                    var methodKeys = _cacheService.GetKeys(methodPattern);
                    if (methodKeys?.Any() == true)
                    {
                        invalidatedCount += (int)_cacheService.RemoveMultiple(methodKeys);
                    }
                }

                // Entity-specific cache'leri temizle (CacheKeyHelper pattern'leri)
                if (_invalidateList)
                {
                    var listPattern = CacheKeyHelper.GenerateListPattern(companyId, entityName);
                    var listKeys = _cacheService.GetKeys(listPattern);
                    if (listKeys?.Any() == true)
                    {
                        invalidatedCount += (int)_cacheService.RemoveMultiple(listKeys);
                    }
                }

                if (_invalidateSearch)
                {
                    var searchPattern = CacheKeyHelper.GenerateSearchPattern(companyId, entityName);
                    var searchKeys = _cacheService.GetKeys(searchPattern);
                    if (searchKeys?.Any() == true)
                    {
                        invalidatedCount += (int)_cacheService.RemoveMultiple(searchKeys);
                    }
                }

                // Entity detail cache'leri temizle
                var entityPattern = CacheKeyHelper.GenerateEntityPattern(companyId, entityName);
                var entityKeys = _cacheService.GetKeys(entityPattern);
                if (entityKeys?.Any() == true)
                {
                    invalidatedCount += (int)_cacheService.RemoveMultiple(entityKeys);
                }

                if (_enableLogging && invalidatedCount > 0)
                {
                    _logger.LogDebug("Entity cache invalidated: {Entity} - {Count} keys removed", entityName, invalidatedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating cache for entity: {Entity}", entityName);
            }

            return invalidatedCount;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Method signature'ını al (logging için)
        /// </summary>
        private string GetMethodSignature(IInvocation invocation)
        {
            return $"{invocation.TargetType.Name}.{invocation.Method.Name}";
        }

        #endregion
    }
}
