namespace Core.Aspects.Autofac.Caching.Models
{
    /// <summary>
    /// Cache Aspect Configuration
    /// appsettings.json'dan cache ayarlarını yönetmek için
    /// </summary>
    public class CacheConfiguration
    {
        /// <summary>
        /// Varsayılan cache süresi (saniye)
        /// </summary>
        public int DefaultDuration { get; set; } = 1800; // 30 dakika

        /// <summary>
        /// Maksimum cache süresi (saniye)
        /// </summary>
        public int MaxDuration { get; set; } = 86400; // 24 saat

        /// <summary>
        /// Minimum cache süresi (saniye)
        /// </summary>
        public int MinDuration { get; set; } = 60; // 1 dakika

        /// <summary>
        /// Performance logging aktif mi
        /// </summary>
        public bool EnablePerformanceLogging { get; set; } = true;

        /// <summary>
        /// Cache warming aktif mi
        /// </summary>
        public bool EnableCacheWarming { get; set; } = false;

        /// <summary>
        /// Cache invalidation delay (ms)
        /// </summary>
        public int InvalidationDelay { get; set; } = 100;

        /// <summary>
        /// Cache key prefix (multi-tenant için)
        /// </summary>
        public string KeyPrefix { get; set; } = "gym";

        /// <summary>
        /// Cache statistics aktif mi
        /// </summary>
        public bool EnableStatistics { get; set; } = true;

        /// <summary>
        /// Cache compression aktif mi (büyük objeler için)
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// Compression threshold (byte)
        /// </summary>
        public int CompressionThreshold { get; set; } = 1024; // 1KB

        /// <summary>
        /// Entity-specific cache durations
        /// </summary>
        public Dictionary<string, int> EntityDurations { get; set; } = new Dictionary<string, int>
        {
            // Hot Data (5 dakika)
            { "User", 300 },
            { "Company", 300 },
            { "Profile", 300 },
            
            // Warm Data (30 dakika)
            { "Member", 1800 },
            { "Payment", 1800 },
            { "Membership", 1800 },
            { "Transaction", 1800 },
            
            // Cold Data (24 saat)
            { "City", 86400 },
            { "Town", 86400 },
            { "MembershipType", 86400 },
            { "Exercise", 86400 },
            { "SystemExercise", 86400 }
        };

        /// <summary>
        /// Cache invalidation rules
        /// </summary>
        public Dictionary<string, string[]> InvalidationRules { get; set; } = new Dictionary<string, string[]>
        {
            { "Member", new[] { "Member", "Membership", "Payment" } },
            { "Payment", new[] { "Payment", "Member", "Transaction" } },
            { "Membership", new[] { "Membership", "Member", "Payment" } },
            { "User", new[] { "User", "Profile" } },
            { "Company", new[] { "Company", "User", "Member" } }
        };

        /// <summary>
        /// Belirli entity için cache süresini al
        /// </summary>
        public int GetDurationForEntity(string entityName)
        {
            if (string.IsNullOrWhiteSpace(entityName))
                return DefaultDuration;

            return EntityDurations.TryGetValue(entityName, out var duration) ? duration : DefaultDuration;
        }

        /// <summary>
        /// Belirli entity için invalidation rule'larını al
        /// </summary>
        public string[] GetInvalidationRulesForEntity(string entityName)
        {
            if (string.IsNullOrWhiteSpace(entityName))
                return new string[0];

            return InvalidationRules.TryGetValue(entityName, out var rules) ? rules : new[] { entityName };
        }

        /// <summary>
        /// Cache süresini validate et
        /// </summary>
        public int ValidateDuration(int duration)
        {
            if (duration < MinDuration) return MinDuration;
            if (duration > MaxDuration) return MaxDuration;
            return duration;
        }
    }

    /// <summary>
    /// Cache Performance Metrics
    /// </summary>
    public class CachePerformanceMetrics
    {
        public string MethodName { get; set; }
        public string CacheKey { get; set; }
        public bool IsHit { get; set; }
        public long ExecutionTimeMs { get; set; }
        public DateTime Timestamp { get; set; }
        public int CompanyId { get; set; }
        public string EntityName { get; set; }
        public int CacheDuration { get; set; }
    }

    /// <summary>
    /// Cache Statistics Summary
    /// </summary>
    public class CacheStatisticsSummary
    {
        public int TotalHits { get; set; }
        public int TotalMisses { get; set; }
        public double HitRatio => TotalHits + TotalMisses > 0 ? (double)TotalHits / (TotalHits + TotalMisses) * 100 : 0;
        public long TotalExecutionTimeMs { get; set; }
        public double AverageExecutionTimeMs => TotalHits + TotalMisses > 0 ? (double)TotalExecutionTimeMs / (TotalHits + TotalMisses) : 0;
        public Dictionary<string, int> EntityHitCounts { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> EntityMissCounts { get; set; } = new Dictionary<string, int>();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }
}
