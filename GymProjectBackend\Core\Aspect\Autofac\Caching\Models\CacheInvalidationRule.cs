namespace Core.Aspects.Autofac.Caching.Models
{
    /// <summary>
    /// Cache Invalidation Rule
    /// Entity değişikliklerinde hangi cache'lerin temizleneceğini belirler
    /// </summary>
    public class CacheInvalidationRule
    {
        /// <summary>
        /// Trigger entity (değişiklik yapılan entity)
        /// </summary>
        public string TriggerEntity { get; set; }

        /// <summary>
        /// Temizlenecek entity'ler
        /// </summary>
        public string[] TargetEntities { get; set; }

        /// <summary>
        /// Method cache'lerini temizle
        /// </summary>
        public bool InvalidateMethodCache { get; set; } = true;

        /// <summary>
        /// Liste cache'lerini temizle
        /// </summary>
        public bool InvalidateListCache { get; set; } = true;

        /// <summary>
        /// Arama cache'lerini temizle
        /// </summary>
        public bool InvalidateSearchCache { get; set; } = true;

        /// <summary>
        /// İstatistik cache'lerini temizle
        /// </summary>
        public bool InvalidateStatsCache { get; set; } = false;

        /// <summary>
        /// Delay (ms) - invalidation'dan önce beklenecek süre
        /// </summary>
        public int DelayMs { get; set; } = 0;

        /// <summary>
        /// Priority - düşük değer yüksek öncelik
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// Açıklama
        /// </summary>
        public string Description { get; set; }
    }

    /// <summary>
    /// Cache Invalidation Rule Manager
    /// Invalidation kurallarını yönetir
    /// </summary>
    public static class CacheInvalidationRuleManager
    {
        /// <summary>
        /// Varsayılan invalidation kuralları
        /// </summary>
        public static readonly Dictionary<string, CacheInvalidationRule> DefaultRules = new Dictionary<string, CacheInvalidationRule>
        {
            // Member değişikliklerinde
            {
                "Member",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Member",
                    TargetEntities = new[] { "Member", "Membership", "Payment", "EntryExit" },
                    InvalidateStatsCache = true,
                    Description = "Member değişikliklerinde ilgili tüm cache'leri temizle"
                }
            },

            // Payment değişikliklerinde
            {
                "Payment",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Payment",
                    TargetEntities = new[] { "Payment", "Member", "Transaction", "RemainingDebt" },
                    InvalidateStatsCache = true,
                    Description = "Payment değişikliklerinde finansal cache'leri temizle"
                }
            },

            // Membership değişikliklerinde
            {
                "Membership",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Membership",
                    TargetEntities = new[] { "Membership", "Member", "Payment", "MembershipFreezeHistory" },
                    InvalidateStatsCache = true,
                    Description = "Membership değişikliklerinde üyelik cache'lerini temizle"
                }
            },

            // User değişikliklerinde
            {
                "User",
                new CacheInvalidationRule
                {
                    TriggerEntity = "User",
                    TargetEntities = new[] { "User", "Profile", "UserCompany" },
                    InvalidateStatsCache = false,
                    Description = "User değişikliklerinde kullanıcı cache'lerini temizle"
                }
            },

            // Company değişikliklerinde
            {
                "Company",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Company",
                    TargetEntities = new[] { "Company", "CompanyAddress", "CompanyUser" },
                    InvalidateStatsCache = false,
                    Description = "Company değişikliklerinde şirket cache'lerini temizle"
                }
            },

            // Transaction değişikliklerinde
            {
                "Transaction",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Transaction",
                    TargetEntities = new[] { "Transaction", "Payment", "Member" },
                    InvalidateStatsCache = true,
                    Description = "Transaction değişikliklerinde finansal cache'leri temizle"
                }
            },

            // Expense değişikliklerinde
            {
                "Expense",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Expense",
                    TargetEntities = new[] { "Expense", "Transaction" },
                    InvalidateStatsCache = true,
                    Description = "Expense değişikliklerinde gider cache'lerini temizle"
                }
            },

            // Product değişikliklerinde
            {
                "Product",
                new CacheInvalidationRule
                {
                    TriggerEntity = "Product",
                    TargetEntities = new[] { "Product", "Transaction" },
                    InvalidateStatsCache = false,
                    Description = "Product değişikliklerinde ürün cache'lerini temizle"
                }
            },

            // MembershipType değişikliklerinde
            {
                "MembershipType",
                new CacheInvalidationRule
                {
                    TriggerEntity = "MembershipType",
                    TargetEntities = new[] { "MembershipType", "Membership" },
                    InvalidateStatsCache = false,
                    Description = "MembershipType değişikliklerinde üyelik tipi cache'lerini temizle"
                }
            },

            // WorkoutProgram değişikliklerinde
            {
                "WorkoutProgram",
                new CacheInvalidationRule
                {
                    TriggerEntity = "WorkoutProgram",
                    TargetEntities = new[] { "WorkoutProgram", "MemberWorkoutProgram", "Exercise" },
                    InvalidateStatsCache = false,
                    Description = "WorkoutProgram değişikliklerinde antrenman cache'lerini temizle"
                }
            }
        };

        /// <summary>
        /// Belirli entity için invalidation rule'ını al
        /// </summary>
        public static CacheInvalidationRule GetRuleForEntity(string entityName)
        {
            if (string.IsNullOrWhiteSpace(entityName))
                return null;

            return DefaultRules.TryGetValue(entityName, out var rule) ? rule : CreateDefaultRule(entityName);
        }

        /// <summary>
        /// Entity için varsayılan rule oluştur
        /// </summary>
        private static CacheInvalidationRule CreateDefaultRule(string entityName)
        {
            return new CacheInvalidationRule
            {
                TriggerEntity = entityName,
                TargetEntities = new[] { entityName },
                Description = $"Default rule for {entityName}"
            };
        }

        /// <summary>
        /// Tüm rule'ları priority'ye göre sıralı al
        /// </summary>
        public static IEnumerable<CacheInvalidationRule> GetAllRulesByPriority()
        {
            return DefaultRules.Values.OrderBy(r => r.Priority);
        }
    }
}
