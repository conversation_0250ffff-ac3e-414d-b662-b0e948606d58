using Castle.DynamicProxy;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace Core.Aspects.Autofac.Caching.Services
{
    /// <summary>
    /// Aspect Cache Key Generator Service
    /// Method signature ve parametrelerine göre cache key oluşturur
    /// Multi-tenant yapıya uygun, collision-safe
    /// </summary>
    public interface IAspectCacheKeyGenerator
    {
        string GenerateKey(IInvocation invocation, int companyId);
        string GenerateMethodKey(IInvocation invocation, int companyId);
        string GenerateParameterHash(IInvocation invocation);
        bool IsValidKey(string key);
    }

    public class AspectCacheKeyGenerator : IAspectCacheKeyGenerator
    {
        #region Fields

        private readonly ILogger<AspectCacheKeyGenerator> _logger;
        private const string METHOD_PREFIX = "method";
        private const string SEPARATOR = ":";

        #endregion

        #region Constructor

        public AspectCacheKeyGenerator(ILogger<AspectCacheKeyGenerator> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Ana cache key oluştur
        /// Pattern: gym:{companyId}:method:{className}:{methodName}:{paramHash}
        /// </summary>
        public string GenerateKey(IInvocation invocation, int companyId)
        {
            try
            {
                if (invocation == null)
                    throw new ArgumentNullException(nameof(invocation));

                if (companyId <= 0)
                {
                    _logger.LogWarning("Invalid company ID for cache key generation: {CompanyId}", companyId);
                    return null;
                }

                var className = GetCleanClassName(invocation.TargetType);
                var methodName = invocation.Method.Name;
                var paramHash = GenerateParameterHash(invocation);

                var key = $"gym{SEPARATOR}{companyId}{SEPARATOR}{METHOD_PREFIX}{SEPARATOR}{className}{SEPARATOR}{methodName}{SEPARATOR}{paramHash}";
                
                _logger.LogTrace("Generated cache key: {Key} for method: {Method}", key, GetMethodSignature(invocation));
                
                return key;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating cache key for method: {Method}", GetMethodSignature(invocation));
                return null;
            }
        }

        /// <summary>
        /// Method-specific cache key oluştur (parametresiz)
        /// Pattern: gym:{companyId}:method:{className}:{methodName}
        /// </summary>
        public string GenerateMethodKey(IInvocation invocation, int companyId)
        {
            try
            {
                if (invocation == null)
                    throw new ArgumentNullException(nameof(invocation));

                if (companyId <= 0)
                    return null;

                var className = GetCleanClassName(invocation.TargetType);
                var methodName = invocation.Method.Name;

                return $"gym{SEPARATOR}{companyId}{SEPARATOR}{METHOD_PREFIX}{SEPARATOR}{className}{SEPARATOR}{methodName}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating method cache key");
                return null;
            }
        }

        /// <summary>
        /// Method parametrelerinden hash oluştur
        /// </summary>
        public string GenerateParameterHash(IInvocation invocation)
        {
            try
            {
                if (invocation?.Arguments == null || invocation.Arguments.Length == 0)
                {
                    return "noparams";
                }

                var parameterData = new List<object>();
                var parameters = invocation.Method.GetParameters();

                for (int i = 0; i < invocation.Arguments.Length; i++)
                {
                    var arg = invocation.Arguments[i];
                    var paramInfo = i < parameters.Length ? parameters[i] : null;
                    
                    parameterData.Add(new 
                    { 
                        Name = paramInfo?.Name ?? $"param{i}",
                        Type = paramInfo?.ParameterType.Name ?? "unknown",
                        Value = SerializeParameter(arg)
                    });
                }

                var json = JsonConvert.SerializeObject(parameterData, Formatting.None);
                var hash = GenerateHash(json);
                
                _logger.LogTrace("Generated parameter hash: {Hash} from {ParamCount} parameters", hash, parameterData.Count);
                
                return hash;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating parameter hash");
                return "hasherror";
            }
        }

        /// <summary>
        /// Cache key'in geçerli olup olmadığını kontrol et
        /// </summary>
        public bool IsValidKey(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return false;

            var parts = key.Split(SEPARATOR);
            
            // Minimum: gym:companyId:method:className:methodName
            if (parts.Length < 5)
                return false;

            // gym prefix kontrolü
            if (parts[0] != "gym")
                return false;

            // CompanyId kontrolü
            if (!int.TryParse(parts[1], out var companyId) || companyId <= 0)
                return false;

            // Method prefix kontrolü
            if (parts[2] != METHOD_PREFIX)
                return false;

            return true;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Class adını temizle (Manager, Service suffix'lerini kaldır)
        /// </summary>
        private string GetCleanClassName(Type type)
        {
            if (type == null)
                return "unknown";

            var name = type.Name;
            
            // Proxy class'ları temizle
            if (name.Contains("Proxy"))
            {
                // Castle proxy class'larından gerçek class adını çıkar
                var baseType = type.BaseType;
                if (baseType != null && baseType != typeof(object))
                {
                    name = baseType.Name;
                }
            }

            // Manager, Service suffix'lerini kaldır
            if (name.EndsWith("Manager"))
                name = name.Substring(0, name.Length - 7);
            else if (name.EndsWith("Service"))
                name = name.Substring(0, name.Length - 7);

            return name.ToLower();
        }

        /// <summary>
        /// Parametreyi güvenli şekilde serialize et
        /// </summary>
        private object SerializeParameter(object parameter)
        {
            if (parameter == null) 
                return null;
            
            var type = parameter.GetType();
            
            // Primitive types ve string
            if (type.IsPrimitive || type == typeof(string) || type == typeof(DateTime) || 
                type == typeof(decimal) || type == typeof(Guid))
            {
                return parameter;
            }

            // Nullable types
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                return parameter;
            }

            // Enum types
            if (type.IsEnum)
            {
                return parameter.ToString();
            }

            // Collections - sadece count
            if (parameter is System.Collections.ICollection collection)
            {
                return new { Type = "Collection", Count = collection.Count };
            }

            // Complex types - ID property'sini bul
            var idProperty = GetIdProperty(type);
            if (idProperty != null)
            {
                var idValue = idProperty.GetValue(parameter);
                return new { Type = type.Name, Id = idValue };
            }
            
            // Fallback - type name ve hash
            return new { Type = type.Name, Hash = parameter.GetHashCode() };
        }

        /// <summary>
        /// Type'dan ID property'sini bul
        /// </summary>
        private PropertyInfo GetIdProperty(Type type)
        {
            // Önce "Id" property'sini ara
            var idProp = type.GetProperty("Id", BindingFlags.Public | BindingFlags.Instance);
            if (idProp != null)
                return idProp;

            // Sonra "ID" property'sini ara
            idProp = type.GetProperty("ID", BindingFlags.Public | BindingFlags.Instance);
            if (idProp != null)
                return idProp;

            // Son olarak "*Id" veya "*ID" pattern'ini ara
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            return properties.FirstOrDefault(p => p.Name.EndsWith("Id") || p.Name.EndsWith("ID"));
        }

        /// <summary>
        /// String'den MD5 hash oluştur
        /// </summary>
        private string GenerateHash(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "empty";

            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);
                return Convert.ToHexString(hashBytes).ToLower();
            }
        }

        /// <summary>
        /// Method signature'ını al (logging için)
        /// </summary>
        private string GetMethodSignature(IInvocation invocation)
        {
            if (invocation?.TargetType == null || invocation.Method == null)
                return "unknown";

            return $"{invocation.TargetType.Name}.{invocation.Method.Name}";
        }

        #endregion
    }
}
