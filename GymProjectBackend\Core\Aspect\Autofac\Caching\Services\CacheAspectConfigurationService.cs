using Core.Aspects.Autofac.Caching.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Core.Aspects.Autofac.Caching.Services
{
    /// <summary>
    /// Cache Aspect Configuration Service
    /// appsettings.json'dan cache ayarlarını yönetir
    /// </summary>
    public interface ICacheAspectConfigurationService
    {
        CacheConfiguration GetConfiguration();
        int GetDurationForEntity(string entityName);
        string[] GetInvalidationRulesForEntity(string entityName);
        bool IsLoggingEnabled();
        bool IsCompressionEnabled();
        int ValidateDuration(int duration);
        void ReloadConfiguration();
    }

    public class CacheAspectConfigurationService : ICacheAspectConfigurationService
    {
        #region Fields

        private readonly IConfiguration _configuration;
        private readonly ILogger<CacheAspectConfigurationService> _logger;
        private CacheConfiguration _cacheConfiguration;
        private readonly object _lockObject = new object();

        #endregion

        #region Constructor

        public CacheAspectConfigurationService(IConfiguration configuration, ILogger<CacheAspectConfigurationService> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            LoadConfiguration();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Cache configuration'ını al
        /// </summary>
        public CacheConfiguration GetConfiguration()
        {
            lock (_lockObject)
            {
                return _cacheConfiguration ?? new CacheConfiguration();
            }
        }

        /// <summary>
        /// Belirli entity için cache süresini al
        /// </summary>
        public int GetDurationForEntity(string entityName)
        {
            var config = GetConfiguration();
            return config.GetDurationForEntity(entityName);
        }

        /// <summary>
        /// Belirli entity için invalidation rule'larını al
        /// </summary>
        public string[] GetInvalidationRulesForEntity(string entityName)
        {
            var config = GetConfiguration();
            return config.GetInvalidationRulesForEntity(entityName);
        }

        /// <summary>
        /// Logging aktif mi
        /// </summary>
        public bool IsLoggingEnabled()
        {
            var config = GetConfiguration();
            return config.EnablePerformanceLogging;
        }

        /// <summary>
        /// Compression aktif mi
        /// </summary>
        public bool IsCompressionEnabled()
        {
            var config = GetConfiguration();
            return config.EnableCompression;
        }

        /// <summary>
        /// Cache süresini validate et
        /// </summary>
        public int ValidateDuration(int duration)
        {
            var config = GetConfiguration();
            return config.ValidateDuration(duration);
        }

        /// <summary>
        /// Configuration'ı yeniden yükle
        /// </summary>
        public void ReloadConfiguration()
        {
            lock (_lockObject)
            {
                LoadConfiguration();
                _logger.LogInformation("Cache aspect configuration reloaded");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Configuration'ı yükle
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var config = new CacheConfiguration();
                
                // appsettings.json'dan CacheAspectSettings section'ını bind et
                var section = _configuration.GetSection("CacheAspectSettings");
                if (section.Exists())
                {
                    section.Bind(config);
                    _logger.LogDebug("Cache aspect configuration loaded from appsettings.json");
                }
                else
                {
                    _logger.LogDebug("CacheAspectSettings section not found, using default configuration");
                }

                // Entity durations'ı yükle
                LoadEntityDurations(config);
                
                // Invalidation rules'ı yükle
                LoadInvalidationRules(config);

                _cacheConfiguration = config;
                
                _logger.LogInformation("Cache aspect configuration loaded successfully. Default duration: {Duration}s", 
                    config.DefaultDuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading cache aspect configuration, using defaults");
                _cacheConfiguration = new CacheConfiguration();
            }
        }

        /// <summary>
        /// Entity duration'larını yükle
        /// </summary>
        private void LoadEntityDurations(CacheConfiguration config)
        {
            try
            {
                var entitySection = _configuration.GetSection("CacheAspectSettings:EntityDurations");
                if (entitySection.Exists())
                {
                    var entityDurations = new Dictionary<string, int>();
                    entitySection.Bind(entityDurations);
                    
                    if (entityDurations.Any())
                    {
                        config.EntityDurations = entityDurations;
                        _logger.LogDebug("Loaded {Count} entity duration configurations", entityDurations.Count);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error loading entity durations, using defaults");
            }
        }

        /// <summary>
        /// Invalidation rules'ı yükle
        /// </summary>
        private void LoadInvalidationRules(CacheConfiguration config)
        {
            try
            {
                var rulesSection = _configuration.GetSection("CacheAspectSettings:InvalidationRules");
                if (rulesSection.Exists())
                {
                    var invalidationRules = new Dictionary<string, string[]>();
                    
                    foreach (var child in rulesSection.GetChildren())
                    {
                        var entityName = child.Key;
                        var rules = child.Get<string[]>();
                        
                        if (rules != null && rules.Length > 0)
                        {
                            invalidationRules[entityName] = rules;
                        }
                    }
                    
                    if (invalidationRules.Any())
                    {
                        config.InvalidationRules = invalidationRules;
                        _logger.LogDebug("Loaded {Count} invalidation rule configurations", invalidationRules.Count);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error loading invalidation rules, using defaults");
            }
        }

        #endregion
    }
}
