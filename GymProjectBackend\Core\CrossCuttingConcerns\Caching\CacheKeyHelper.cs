using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Multi-tenant Cache Key Helper
    /// Hierarchical cache key generation ve management için
    /// Pattern: gym:{companyId}:{entity}:{id}
    /// </summary>
    public static class CacheKeyHelper
    {
        #region Constants

        /// <summary>
        /// Ana namespace prefix - tüm gym cache key'leri bu ile başlar
        /// </summary>
        public const string ROOT_PREFIX = "gym";

        /// <summary>
        /// Key separator - cache key bölümlerini ayırmak için
        /// </summary>
        public const string SEPARATOR = ":";

        /// <summary>
        /// Wildcard pattern - pattern-based operations için
        /// </summary>
        public const string WILDCARD = "*";

        #endregion

        #region Entity Names

        /// <summary>
        /// Cache'lenebilir entity isimleri
        /// Yeni entity eklendiğinde buraya eklenmeli
        /// </summary>
        public static class EntityNames
        {
            public const string MEMBER = "member";
            public const string PAYMENT = "payment";
            public const string USER = "user";
            public const string COMPANY = "company";
            public const string MEMBERSHIP = "membership";
            public const string MEMBERSHIP_TYPE = "membershiptype";
            public const string PRODUCT = "product";
            public const string TRANSACTION = "transaction";
            public const string EXPENSE = "expense";
            public const string ENTRY_EXIT = "entryexit";
            public const string WORKOUT_PROGRAM = "workoutprogram";
            public const string EXERCISE = "exercise";
            public const string CITY = "city";
            public const string TOWN = "town";
            public const string STATISTICS = "statistics";
            public const string REPORT = "report";
        }

        #endregion

        #region Basic Key Generation

        /// <summary>
        /// Temel cache key oluştur
        /// Pattern: gym:{companyId}:{entity}:{id}
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entityName">Entity adı (EntityNames'den kullan)</param>
        /// <param name="entityId">Entity ID</param>
        /// <returns>Cache key</returns>
        public static string GenerateKey(int companyId, string entityName, object entityId)
        {
            if (companyId <= 0)
                throw new ArgumentException("CompanyId must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entityName))
                throw new ArgumentException("EntityName cannot be null or empty", nameof(entityName));
            
            if (entityId == null)
                throw new ArgumentException("EntityId cannot be null", nameof(entityId));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entityName.ToLower()}{SEPARATOR}{entityId}";
        }

        /// <summary>
        /// Liste cache key oluştur (sayfalama için)
        /// Pattern: gym:{companyId}:{entity}:list:{page}:{pageSize}
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <param name="page">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Liste cache key</returns>
        public static string GenerateListKey(int companyId, string entityName, int page = 1, int pageSize = 10)
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entityName.ToLower()}{SEPARATOR}list{SEPARATOR}{page}{SEPARATOR}{pageSize}";
        }

        /// <summary>
        /// Arama cache key oluştur
        /// Pattern: gym:{companyId}:{entity}:search:{searchTerm}:{page}
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <param name="page">Sayfa numarası</param>
        /// <returns>Arama cache key</returns>
        public static string GenerateSearchKey(int companyId, string entityName, string searchTerm, int page = 1)
        {
            var cleanSearchTerm = CleanSearchTerm(searchTerm);
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entityName.ToLower()}{SEPARATOR}search{SEPARATOR}{cleanSearchTerm}{SEPARATOR}{page}";
        }

        /// <summary>
        /// İstatistik cache key oluştur
        /// Pattern: gym:{companyId}:stats:{statType}:{period}
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="statType">İstatistik tipi (daily, monthly, yearly)</param>
        /// <param name="period">Dönem (2024-01, 2024 vb.)</param>
        /// <returns>İstatistik cache key</returns>
        public static string GenerateStatsKey(int companyId, string statType, string period)
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{EntityNames.STATISTICS}{SEPARATOR}{statType.ToLower()}{SEPARATOR}{period}";
        }

        #endregion

        #region Pattern Generation

        /// <summary>
        /// Company'nin tüm cache'leri için pattern
        /// Pattern: gym:{companyId}:*
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <returns>Company pattern</returns>
        public static string GenerateCompanyPattern(int companyId)
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{WILDCARD}";
        }

        /// <summary>
        /// Belirli entity'nin tüm cache'leri için pattern
        /// Pattern: gym:{companyId}:{entity}:*
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <returns>Entity pattern</returns>
        public static string GenerateEntityPattern(int companyId, string entityName)
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entityName.ToLower()}{SEPARATOR}{WILDCARD}";
        }

        /// <summary>
        /// Belirli entity tipinin tüm liste cache'leri için pattern
        /// Pattern: gym:{companyId}:{entity}:list:*
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <returns>Liste pattern</returns>
        public static string GenerateListPattern(int companyId, string entityName)
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entityName.ToLower()}{SEPARATOR}list{SEPARATOR}{WILDCARD}";
        }

        /// <summary>
        /// Belirli entity tipinin tüm arama cache'leri için pattern
        /// Pattern: gym:{companyId}:{entity}:search:*
        /// </summary>
        /// <param name="companyId">Şirket ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <returns>Arama pattern</returns>
        public static string GenerateSearchPattern(int companyId, string entityName)
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entityName.ToLower()}{SEPARATOR}search{SEPARATOR}{WILDCARD}";
        }

        /// <summary>
        /// Tüm gym cache'leri için global pattern
        /// Pattern: gym:*
        /// </summary>
        /// <returns>Global pattern</returns>
        public static string GenerateGlobalPattern()
        {
            return $"{ROOT_PREFIX}{SEPARATOR}{WILDCARD}";
        }

        #endregion

        #region Key Parsing

        /// <summary>
        /// Cache key'den company ID'yi çıkar
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Company ID veya null</returns>
        public static int? ExtractCompanyId(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return null;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length >= 2 && parts[0] == ROOT_PREFIX)
            {
                if (int.TryParse(parts[1], out var companyId))
                    return companyId;
            }

            return null;
        }

        /// <summary>
        /// Cache key'den entity name'i çıkar
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Entity name veya null</returns>
        public static string ExtractEntityName(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return null;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length >= 3 && parts[0] == ROOT_PREFIX)
            {
                return parts[2];
            }

            return null;
        }

        /// <summary>
        /// Cache key'in geçerli olup olmadığını kontrol et
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Geçerli mi</returns>
        public static bool IsValidKey(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return false;

            var parts = cacheKey.Split(SEPARATOR);
            return parts.Length >= 3 && 
                   parts[0] == ROOT_PREFIX && 
                   int.TryParse(parts[1], out var companyId) && 
                   companyId > 0 &&
                   !string.IsNullOrWhiteSpace(parts[2]);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Arama terimini cache key için temizle
        /// </summary>
        /// <param name="searchTerm">Arama terimi</param>
        /// <returns>Temizlenmiş arama terimi</returns>
        private static string CleanSearchTerm(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return "empty";

            // Cache key'de sorun çıkarabilecek karakterleri temizle
            var cleaned = searchTerm.ToLower()
                                   .Replace(" ", "_")
                                   .Replace(":", "_")
                                   .Replace("*", "_")
                                   .Replace("?", "_")
                                   .Replace("[", "_")
                                   .Replace("]", "_");

            // Maksimum 50 karakter
            if (cleaned.Length > 50)
                cleaned = cleaned.Substring(0, 50);

            return cleaned;
        }

        /// <summary>
        /// Cache key'leri company ID'ye göre grupla
        /// </summary>
        /// <param name="cacheKeys">Cache key listesi</param>
        /// <returns>Company ID'ye göre gruplandırılmış key'ler</returns>
        public static Dictionary<int, List<string>> GroupKeysByCompany(IEnumerable<string> cacheKeys)
        {
            var result = new Dictionary<int, List<string>>();

            foreach (var key in cacheKeys ?? Enumerable.Empty<string>())
            {
                var companyId = ExtractCompanyId(key);
                if (companyId.HasValue)
                {
                    if (!result.ContainsKey(companyId.Value))
                        result[companyId.Value] = new List<string>();
                    
                    result[companyId.Value].Add(key);
                }
            }

            return result;
        }

        #endregion
    }
}
