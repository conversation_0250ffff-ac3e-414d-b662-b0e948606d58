using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache Service Interface - Multi-tenant Redis Cache için
    /// 100K+ kullanıcı ve 1000+ salon için optimize edilmiş
    /// </summary>
    public interface ICacheService
    {
        #region Synchronous Methods

        /// <summary>
        /// Cache'den veri al - Generic type support
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        T Get<T>(string key);

        /// <summary>
        /// Cache'e veri kaydet - Generic type support
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void Set<T>(string key, T value, TimeSpan? expiry = null);

        /// <summary>
        /// Cache'den veri sil
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        bool Remove(string key);

        /// <summary>
        /// Cache key'in var olup olmadığını kontrol et
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key var mı</returns>
        bool Exists(string key);

        /// <summary>
        /// Pattern'e göre key'leri getir
        /// </summary>
        /// <param name="pattern">Arama pattern'i (örn: "gym:1:*")</param>
        /// <returns>Bulunan key'ler</returns>
        IEnumerable<string> GetKeys(string pattern);

        /// <summary>
        /// Pattern'e göre cache'leri sil
        /// </summary>
        /// <param name="pattern">Silme pattern'i</param>
        /// <returns>Silinen key sayısı</returns>
        long RemoveByPattern(string pattern);

        /// <summary>
        /// Cache'e TTL (Time To Live) set et
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="expiry">Expiry süresi</param>
        /// <returns>İşlem başarılı mı</returns>
        bool SetExpiry(string key, TimeSpan expiry);

        /// <summary>
        /// Key'in kalan TTL'ini getir
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Kalan süre</returns>
        TimeSpan? GetTimeToLive(string key);

        #endregion

        #region Asynchronous Methods

        /// <summary>
        /// Cache'den veri al - Async
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        Task<T> GetAsync<T>(string key);

        /// <summary>
        /// Cache'e veri kaydet - Async
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null);

        /// <summary>
        /// Cache'den veri sil - Async
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        Task<bool> RemoveAsync(string key);

        /// <summary>
        /// Cache key'in var olup olmadığını kontrol et - Async
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key var mı</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Pattern'e göre key'leri getir - Async
        /// </summary>
        /// <param name="pattern">Arama pattern'i</param>
        /// <returns>Bulunan key'ler</returns>
        Task<IEnumerable<string>> GetKeysAsync(string pattern);

        /// <summary>
        /// Pattern'e göre cache'leri sil - Async
        /// </summary>
        /// <param name="pattern">Silme pattern'i</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> RemoveByPatternAsync(string pattern);

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Birden fazla key'i aynı anda getir
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="keys">Key listesi</param>
        /// <returns>Key-Value dictionary</returns>
        Dictionary<string, T> GetMultiple<T>(IEnumerable<string> keys);

        /// <summary>
        /// Birden fazla key'e aynı anda veri kaydet
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="keyValuePairs">Key-Value çiftleri</param>
        /// <param name="expiry">Expiry süresi (opsiyonel)</param>
        void SetMultiple<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null);

        /// <summary>
        /// Birden fazla key'i aynı anda sil
        /// </summary>
        /// <param name="keys">Silinecek key'ler</param>
        /// <returns>Silinen key sayısı</returns>
        long RemoveMultiple(IEnumerable<string> keys);

        #endregion

        #region Health & Statistics

        /// <summary>
        /// Redis bağlantısının sağlıklı olup olmadığını kontrol et
        /// </summary>
        /// <returns>Bağlantı sağlıklı mı</returns>
        bool IsHealthy();

        /// <summary>
        /// Cache istatistiklerini getir
        /// </summary>
        /// <returns>Cache statistics</returns>
        CacheStatistics GetStatistics();

        /// <summary>
        /// Cache'i temizle (tüm database)
        /// </summary>
        void FlushDatabase();

        /// <summary>
        /// Redis ping testi
        /// </summary>
        /// <returns>Ping süresi (ms)</returns>
        long Ping();

        #endregion
    }

    /// <summary>
    /// Cache istatistikleri için model
    /// </summary>
    public class CacheStatistics
    {
        public long TotalKeys { get; set; }
        public long UsedMemory { get; set; }
        public long MaxMemory { get; set; }
        public double MemoryUsagePercentage { get; set; }
        public long HitCount { get; set; }
        public long MissCount { get; set; }
        public double HitRatio { get; set; }
        public int ConnectedClients { get; set; }
        public TimeSpan Uptime { get; set; }
        public string RedisVersion { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}
