using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Redis Cache Service Implementation
    /// Multi-tenant, 100K+ kullanıcı için optimize edilmiş
    /// </summary>
    public class RedisCacheService : ICacheService, IDisposable
    {
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly IDatabase _database;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly TimeSpan _defaultExpiry;
        private readonly JsonSerializerSettings _jsonSettings;
        private bool _disposed = false;

        public RedisCacheService(IConnectionMultiplexer connectionMultiplexer, 
                                IConfiguration configuration, 
                                ILogger<RedisCacheService> logger)
        {
            _connectionMultiplexer = connectionMultiplexer ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Environment'a göre database seç
            var environment = configuration["Environment"] ?? "dev";
            var databaseNumber = configuration.GetValue<int>($"RedisSettings:{environment}:Database");
            var defaultExpirySeconds = configuration.GetValue<int>($"RedisSettings:{environment}:DefaultExpiry");
            
            _database = _connectionMultiplexer.GetDatabase(databaseNumber);
            _defaultExpiry = TimeSpan.FromSeconds(defaultExpirySeconds);
            
            // JSON serialization ayarları (performans için optimize)
            _jsonSettings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                NullValueHandling = NullValueHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Ignore
            };

            _logger.LogInformation("RedisCacheService initialized for environment: {Environment}, Database: {Database}", 
                                 environment, databaseNumber);
        }

        #region Synchronous Methods

        public T Get<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = _database.StringGet(key);
                
                if (!value.HasValue)
                {
                    _logger.LogDebug("Cache miss for key: {Key}", key);
                    return default(T);
                }

                _logger.LogDebug("Cache hit for key: {Key}", key);
                return JsonConvert.DeserializeObject<T>(value, _jsonSettings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
                return default(T);
            }
        }

        public void Set<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var serializedValue = JsonConvert.SerializeObject(value, _jsonSettings);
                var expiryTime = expiry ?? _defaultExpiry;
                
                _database.StringSet(key, serializedValue, expiryTime);
                _logger.LogDebug("Cache set for key: {Key}, Expiry: {Expiry}", key, expiryTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
            }
        }

        public bool Remove(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = _database.KeyDelete(key);
                _logger.LogDebug("Cache remove for key: {Key}, Success: {Success}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
                return false;
            }
        }

        public bool Exists(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return _database.KeyExists(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
                return false;
            }
        }

        public IEnumerable<string> GetKeys(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return Enumerable.Empty<string>();

                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                return server.Keys(_database.Database, pattern).Select(k => k.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting keys with pattern: {Pattern}", pattern);
                return Enumerable.Empty<string>();
            }
        }

        public long RemoveByPattern(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return 0;

                var keys = GetKeys(pattern).ToArray();
                if (keys.Length == 0)
                    return 0;

                var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
                var deletedCount = _database.KeyDelete(redisKeys);
                
                _logger.LogDebug("Cache remove by pattern: {Pattern}, Deleted: {Count}", pattern, deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache by pattern: {Pattern}", pattern);
                return 0;
            }
        }

        public bool SetExpiry(string key, TimeSpan expiry)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return _database.KeyExpire(key, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting expiry for key: {Key}", key);
                return false;
            }
        }

        public TimeSpan? GetTimeToLive(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return null;

                return _database.KeyTimeToLive(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting TTL for key: {Key}", key);
                return null;
            }
        }

        #endregion

        #region Asynchronous Methods

        public async Task<T> GetAsync<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = await _database.StringGetAsync(key);
                
                if (!value.HasValue)
                {
                    _logger.LogDebug("Cache miss for key: {Key}", key);
                    return default(T);
                }

                _logger.LogDebug("Cache hit for key: {Key}", key);
                return JsonConvert.DeserializeObject<T>(value, _jsonSettings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value async for key: {Key}", key);
                return default(T);
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var serializedValue = JsonConvert.SerializeObject(value, _jsonSettings);
                var expiryTime = expiry ?? _defaultExpiry;
                
                await _database.StringSetAsync(key, serializedValue, expiryTime);
                _logger.LogDebug("Cache set async for key: {Key}, Expiry: {Expiry}", key, expiryTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value async for key: {Key}", key);
            }
        }

        public async Task<bool> RemoveAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = await _database.KeyDeleteAsync(key);
                _logger.LogDebug("Cache remove async for key: {Key}, Success: {Success}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value async for key: {Key}", key);
                return false;
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return await _database.KeyExistsAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence async for key: {Key}", key);
                return false;
            }
        }

        public async Task<IEnumerable<string>> GetKeysAsync(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return Enumerable.Empty<string>();

                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var keys = server.KeysAsync(_database.Database, pattern);
                var result = new List<string>();
                
                await foreach (var key in keys)
                {
                    result.Add(key.ToString());
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting keys async with pattern: {Pattern}", pattern);
                return Enumerable.Empty<string>();
            }
        }

        public async Task<long> RemoveByPatternAsync(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return 0;

                var keys = (await GetKeysAsync(pattern)).ToArray();
                if (keys.Length == 0)
                    return 0;

                var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
                var deletedCount = await _database.KeyDeleteAsync(redisKeys);
                
                _logger.LogDebug("Cache remove by pattern async: {Pattern}, Deleted: {Count}", pattern, deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache by pattern async: {Pattern}", pattern);
                return 0;
            }
        }

        #endregion

        #region Bulk Operations

        public Dictionary<string, T> GetMultiple<T>(IEnumerable<string> keys)
        {
            try
            {
                var keyArray = keys?.ToArray();
                if (keyArray == null || keyArray.Length == 0)
                    return new Dictionary<string, T>();

                var redisKeys = keyArray.Select(k => (RedisKey)k).ToArray();
                var values = _database.StringGet(redisKeys);

                var result = new Dictionary<string, T>();
                for (int i = 0; i < keyArray.Length; i++)
                {
                    if (values[i].HasValue)
                    {
                        try
                        {
                            result[keyArray[i]] = JsonConvert.DeserializeObject<T>(values[i], _jsonSettings);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error deserializing value for key: {Key}", keyArray[i]);
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting multiple cache values");
                return new Dictionary<string, T>();
            }
        }

        public void SetMultiple<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiry = null)
        {
            try
            {
                if (keyValuePairs == null || keyValuePairs.Count == 0)
                    return;

                var expiryTime = expiry ?? _defaultExpiry;
                var batch = _database.CreateBatch();

                foreach (var kvp in keyValuePairs)
                {
                    if (!string.IsNullOrWhiteSpace(kvp.Key) && kvp.Value != null)
                    {
                        var serializedValue = JsonConvert.SerializeObject(kvp.Value, _jsonSettings);
                        batch.StringSetAsync(kvp.Key, serializedValue, expiryTime);
                    }
                }

                batch.Execute();
                _logger.LogDebug("Cache set multiple: {Count} items", keyValuePairs.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting multiple cache values");
            }
        }

        public long RemoveMultiple(IEnumerable<string> keys)
        {
            try
            {
                var keyArray = keys?.Where(k => !string.IsNullOrWhiteSpace(k)).ToArray();
                if (keyArray == null || keyArray.Length == 0)
                    return 0;

                var redisKeys = keyArray.Select(k => (RedisKey)k).ToArray();
                var deletedCount = _database.KeyDelete(redisKeys);

                _logger.LogDebug("Cache remove multiple: {Count} items deleted", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing multiple cache values");
                return 0;
            }
        }

        #endregion

        #region Health & Statistics

        public bool IsHealthy()
        {
            try
            {
                return _connectionMultiplexer.IsConnected && _database.Ping().TotalMilliseconds < 1000;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking Redis health");
                return false;
            }
        }

        public CacheStatistics GetStatistics()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var info = server.Info();

                var stats = new CacheStatistics
                {
                    LastUpdated = DateTime.UtcNow
                };

                foreach (var section in info)
                {
                    foreach (var item in section)
                    {
                        switch (item.Key.ToLower())
                        {
                            case "used_memory":
                                if (long.TryParse(item.Value, out var usedMemory))
                                    stats.UsedMemory = usedMemory;
                                break;
                            case "maxmemory":
                                if (long.TryParse(item.Value, out var maxMemory))
                                    stats.MaxMemory = maxMemory;
                                break;
                            case "connected_clients":
                                if (int.TryParse(item.Value, out var connectedClients))
                                    stats.ConnectedClients = connectedClients;
                                break;
                            case "redis_version":
                                stats.RedisVersion = item.Value;
                                break;
                            case "uptime_in_seconds":
                                if (int.TryParse(item.Value, out var uptimeSeconds))
                                    stats.Uptime = TimeSpan.FromSeconds(uptimeSeconds);
                                break;
                        }
                    }
                }

                // Memory usage percentage hesapla
                if (stats.MaxMemory > 0)
                {
                    stats.MemoryUsagePercentage = (double)stats.UsedMemory / stats.MaxMemory * 100;
                }

                // Key count'u al
                stats.TotalKeys = server.DatabaseSize(_database.Database);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache statistics");
                return new CacheStatistics { LastUpdated = DateTime.UtcNow };
            }
        }

        public void FlushDatabase()
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                server.FlushDatabase(_database.Database);
                _logger.LogWarning("Cache database flushed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error flushing cache database");
            }
        }

        public long Ping()
        {
            try
            {
                var latency = _database.Ping();
                return (long)latency.TotalMilliseconds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pinging Redis");
                return -1;
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _connectionMultiplexer?.Dispose();
                _disposed = true;
            }
        }

        #endregion
    }
}
