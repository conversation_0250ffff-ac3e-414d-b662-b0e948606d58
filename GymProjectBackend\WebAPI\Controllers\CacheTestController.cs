using Microsoft.AspNetCore.Mvc;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Caching.Services;
using System;
using System.Collections.Generic;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Redis Cache Test Controller
    /// Development ortamında cache test işlemleri için
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CacheTestController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly ICacheManager _cacheManager;
        private readonly ICompanyContext _companyContext;
        private readonly IAspectCacheKeyGenerator _aspectCacheKeyGenerator;
        private readonly ICacheAspectConfigurationService _cacheAspectConfigurationService;

        public CacheTestController(
            ICacheService cacheService,
            ICacheManager cacheManager,
            ICompanyContext companyContext,
            IAspectCacheKeyGenerator aspectCacheKeyGenerator,
            ICacheAspectConfigurationService cacheAspectConfigurationService)
        {
            _cacheService = cacheService;
            _cacheManager = cacheManager;
            _companyContext = companyContext;
            _aspectCacheKeyGenerator = aspectCacheKeyGenerator;
            _cacheAspectConfigurationService = cacheAspectConfigurationService;
        }

        /// <summary>
        /// Redis bağlantı testi - Ping/Pong
        /// </summary>
        [HttpGet("ping")]
        public IActionResult Ping()
        {
            try
            {
                var pingTime = _cacheService.Ping();
                
                if (pingTime >= 0)
                {
                    return Ok(new SuccessDataResult<object>(new 
                    { 
                        message = "Redis connection successful", 
                        pingTime = $"{pingTime}ms",
                        timestamp = DateTime.UtcNow
                    }));
                }
                else
                {
                    return BadRequest(new ErrorResult("Redis connection failed"));
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Redis connection error: {ex.Message}"));
            }
        }

        /// <summary>
        /// Redis health check
        /// </summary>
        [HttpGet("health")]
        public IActionResult Health()
        {
            try
            {
                var isHealthy = _cacheService.IsHealthy();
                var statistics = _cacheService.GetStatistics();
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    isHealthy,
                    statistics,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Health check failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Temel cache set/get testi
        /// </summary>
        [HttpPost("test-basic")]
        public IActionResult TestBasic([FromBody] TestCacheRequest request)
        {
            try
            {
                // Cache'e veri kaydet
                _cacheService.Set(request.Key, request.Value, TimeSpan.FromMinutes(5));
                
                // Cache'den veri oku
                var cachedValue = _cacheService.Get<string>(request.Key);
                
                // Key'in var olup olmadığını kontrol et
                var exists = _cacheService.Exists(request.Key);
                
                // TTL kontrol et
                var ttl = _cacheService.GetTimeToLive(request.Key);
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Cache test successful",
                    originalValue = request.Value,
                    cachedValue,
                    exists,
                    ttl = ttl?.TotalSeconds,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Generic type cache testi
        /// </summary>
        [HttpPost("test-generic")]
        public IActionResult TestGeneric()
        {
            try
            {
                var testObject = new TestObject
                {
                    Id = 123,
                    Name = "Test User",
                    Email = "<EMAIL>",
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                var key = "test:object:123";
                
                // Object cache'e kaydet
                _cacheService.Set(key, testObject, TimeSpan.FromMinutes(10));
                
                // Object cache'den oku
                var cachedObject = _cacheService.Get<TestObject>(key);
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Generic cache test successful",
                    originalObject = testObject,
                    cachedObject,
                    isEqual = testObject.Id == cachedObject?.Id && testObject.Name == cachedObject?.Name,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Generic cache test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Pattern-based operations testi
        /// </summary>
        [HttpPost("test-pattern")]
        public IActionResult TestPattern()
        {
            try
            {
                var testKeys = new List<string>
                {
                    "gym:1:member:101",
                    "gym:1:member:102", 
                    "gym:1:payment:201",
                    "gym:2:member:301"
                };

                // Test verileri kaydet
                foreach (var key in testKeys)
                {
                    _cacheService.Set(key, $"Value for {key}", TimeSpan.FromMinutes(5));
                }

                // Pattern ile key'leri getir
                var gym1Keys = _cacheService.GetKeys("gym:1:*");
                var memberKeys = _cacheService.GetKeys("gym:*:member:*");
                
                return Ok(new SuccessDataResult<object>(new 
                { 
                    message = "Pattern test successful",
                    testKeys,
                    gym1Keys,
                    memberKeys,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Pattern test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache temizleme testi
        /// </summary>
        [HttpDelete("test-cleanup")]
        public IActionResult TestCleanup()
        {
            try
            {
                // Test pattern'ine göre cache'leri temizle
                var deletedCount = _cacheService.RemoveByPattern("test:*");

                return Ok(new SuccessDataResult<object>(new
                {
                    message = "Cache cleanup successful",
                    deletedCount,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache cleanup failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Multi-tenant cache key pattern testi
        /// </summary>
        [HttpPost("test-multitenant")]
        public IActionResult TestMultiTenant()
        {
            try
            {
                var currentCompanyId = _companyContext.GetCompanyId();

                // Test için farklı company ID'leri simüle et
                var testData = new List<object>();

                // Company 1 test data
                var company1Key = CacheKeyHelper.GenerateKey(1, CacheKeyHelper.EntityNames.MEMBER, 123);
                _cacheService.Set(company1Key, "Company 1 - Member 123", TimeSpan.FromMinutes(5));

                // Company 2 test data
                var company2Key = CacheKeyHelper.GenerateKey(2, CacheKeyHelper.EntityNames.MEMBER, 123);
                _cacheService.Set(company2Key, "Company 2 - Member 123", TimeSpan.FromMinutes(5));

                // Company 1 list data
                var company1ListKey = CacheKeyHelper.GenerateListKey(1, CacheKeyHelper.EntityNames.MEMBER, 1, 10);
                _cacheService.Set(company1ListKey, new List<string> { "Member 1", "Member 2", "Member 3" }, TimeSpan.FromMinutes(5));

                // Company 2 list data
                var company2ListKey = CacheKeyHelper.GenerateListKey(2, CacheKeyHelper.EntityNames.MEMBER, 1, 10);
                _cacheService.Set(company2ListKey, new List<string> { "Member A", "Member B", "Member C" }, TimeSpan.FromMinutes(5));

                // Pattern'leri test et
                var company1Pattern = CacheKeyHelper.GenerateCompanyPattern(1);
                var company2Pattern = CacheKeyHelper.GenerateCompanyPattern(2);
                var memberPattern = CacheKeyHelper.GenerateEntityPattern(1, CacheKeyHelper.EntityNames.MEMBER);

                var company1Keys = _cacheService.GetKeys(company1Pattern);
                var company2Keys = _cacheService.GetKeys(company2Pattern);
                var memberKeys = _cacheService.GetKeys(memberPattern);

                // Verileri oku
                var company1Member = _cacheService.Get<string>(company1Key);
                var company2Member = _cacheService.Get<string>(company2Key);
                var company1List = _cacheService.Get<List<string>>(company1ListKey);
                var company2List = _cacheService.Get<List<string>>(company2ListKey);

                return Ok(new SuccessDataResult<object>(new
                {
                    message = "Multi-tenant cache test successful",
                    currentCompanyId,
                    testResults = new
                    {
                        company1Key,
                        company2Key,
                        company1ListKey,
                        company2ListKey,
                        company1Member,
                        company2Member,
                        company1List,
                        company2List,
                        patterns = new
                        {
                            company1Pattern,
                            company2Pattern,
                            memberPattern
                        },
                        foundKeys = new
                        {
                            company1Keys,
                            company2Keys,
                            memberKeys
                        }
                    },
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Multi-tenant cache test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// CacheManager ile multi-tenant test
        /// </summary>
        [HttpPost("test-cache-manager")]
        public IActionResult TestCacheManager()
        {
            try
            {
                var currentCompanyId = _companyContext.GetCompanyId();

                if (currentCompanyId <= 0)
                {
                    return BadRequest(new ErrorResult("Invalid company context. Please login with a valid company user."));
                }

                // CacheManager ile test verileri kaydet
                _cacheManager.Set(CacheKeyHelper.EntityNames.MEMBER, 101, new { Id = 101, Name = "John Doe", Email = "<EMAIL>" });
                _cacheManager.Set(CacheKeyHelper.EntityNames.MEMBER, 102, new { Id = 102, Name = "Jane Smith", Email = "<EMAIL>" });

                // Liste cache'i kaydet
                _cacheManager.SetList(CacheKeyHelper.EntityNames.MEMBER,
                    new List<object>
                    {
                        new { Id = 101, Name = "John Doe" },
                        new { Id = 102, Name = "Jane Smith" }
                    }, 1, 10);

                // Arama cache'i kaydet
                _cacheManager.SetSearch(CacheKeyHelper.EntityNames.MEMBER, "john",
                    new List<object> { new { Id = 101, Name = "John Doe" } });

                // İstatistik cache'i kaydet
                _cacheManager.SetStats("daily", "2024-07-27", new { TotalMembers = 2, ActiveMembers = 2 });

                // Verileri oku
                var member101 = _cacheManager.Get<object>(CacheKeyHelper.EntityNames.MEMBER, 101);
                var member102 = _cacheManager.Get<object>(CacheKeyHelper.EntityNames.MEMBER, 102);
                var memberList = _cacheManager.GetList<List<object>>(CacheKeyHelper.EntityNames.MEMBER, 1, 10);
                var searchResult = _cacheManager.GetSearch<List<object>>(CacheKeyHelper.EntityNames.MEMBER, "john");
                var dailyStats = _cacheManager.GetStats<object>("daily", "2024-07-27");

                // Bulk operations test
                var bulkIds = new List<object> { 101, 102 };
                var bulkResults = _cacheManager.GetMultiple<object>(CacheKeyHelper.EntityNames.MEMBER, bulkIds);

                return Ok(new SuccessDataResult<object>(new
                {
                    message = "CacheManager test successful",
                    currentCompanyId,
                    testResults = new
                    {
                        member101,
                        member102,
                        memberList,
                        searchResult,
                        dailyStats,
                        bulkResults
                    },
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"CacheManager test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache Aspect Configuration testi
        /// </summary>
        [HttpGet("test-aspect-config")]
        public IActionResult TestAspectConfiguration()
        {
            try
            {
                var config = _cacheAspectConfigurationService.GetConfiguration();

                var testResults = new
                {
                    defaultDuration = config.DefaultDuration,
                    maxDuration = config.MaxDuration,
                    minDuration = config.MinDuration,
                    enableLogging = config.EnablePerformanceLogging,
                    enableCompression = config.EnableCompression,
                    memberDuration = config.GetDurationForEntity("Member"),
                    userDuration = config.GetDurationForEntity("User"),
                    cityDuration = config.GetDurationForEntity("City"),
                    memberInvalidationRules = config.GetInvalidationRulesForEntity("Member"),
                    paymentInvalidationRules = config.GetInvalidationRulesForEntity("Payment"),
                    validatedDuration = config.ValidateDuration(50), // Min'den küçük
                    validatedMaxDuration = config.ValidateDuration(100000) // Max'tan büyük
                };

                return Ok(new SuccessDataResult<object>(new
                {
                    message = "Cache aspect configuration test successful",
                    configuration = testResults,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache aspect configuration test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache Aspect Key Generator testi
        /// </summary>
        [HttpPost("test-aspect-keygen")]
        public IActionResult TestAspectKeyGeneration()
        {
            try
            {
                var currentCompanyId = _companyContext.GetCompanyId();

                if (currentCompanyId <= 0)
                {
                    return BadRequest(new ErrorResult("Invalid company context. Please login with a valid company user."));
                }

                // Mock invocation data oluştur (gerçek aspect test için)
                var testResults = new
                {
                    companyId = currentCompanyId,
                    sampleKeys = new
                    {
                        memberKey = $"gym:{currentCompanyId}:method:member:getbyid:hash123",
                        paymentKey = $"gym:{currentCompanyId}:method:payment:getall:noparams",
                        userKey = $"gym:{currentCompanyId}:method:user:getprofile:hash456"
                    },
                    keyValidation = new
                    {
                        validKey = _aspectCacheKeyGenerator.IsValidKey($"gym:{currentCompanyId}:method:member:getbyid:hash123"),
                        invalidKey1 = _aspectCacheKeyGenerator.IsValidKey("invalid:key"),
                        invalidKey2 = _aspectCacheKeyGenerator.IsValidKey($"gym:0:method:member:getbyid:hash123"),
                        invalidKey3 = _aspectCacheKeyGenerator.IsValidKey("gym:1:invalid:member:getbyid:hash123")
                    }
                };

                return Ok(new SuccessDataResult<object>(new
                {
                    message = "Cache aspect key generation test successful",
                    testResults,
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache aspect key generation test failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache Aspect simülasyon testi (gerçek aspect olmadan)
        /// </summary>
        [HttpPost("test-aspect-simulation")]
        public IActionResult TestAspectSimulation([FromBody] AspectTestRequest request)
        {
            try
            {
                var currentCompanyId = _companyContext.GetCompanyId();

                if (currentCompanyId <= 0)
                {
                    return BadRequest(new ErrorResult("Invalid company context. Please login with a valid company user."));
                }

                // Simulated cache key generation
                var cacheKey = $"gym:{currentCompanyId}:method:{request.ClassName}:{request.MethodName}:{request.ParameterHash}";

                // Cache'den kontrol et
                var cachedValue = _cacheService.Get<object>(cacheKey);
                var isHit = cachedValue != null;

                if (!isHit)
                {
                    // Cache MISS - simulated method execution
                    var simulatedResult = new
                    {
                        id = request.EntityId,
                        name = $"Simulated {request.EntityType} {request.EntityId}",
                        companyId = currentCompanyId,
                        executedAt = DateTime.UtcNow,
                        fromCache = false
                    };

                    // Cache'e kaydet
                    var duration = _cacheAspectConfigurationService.GetDurationForEntity(request.EntityType);
                    _cacheService.Set(cacheKey, simulatedResult, TimeSpan.FromSeconds(duration));

                    cachedValue = simulatedResult;
                }
                else
                {
                    // Cache HIT - update fromCache flag
                    if (cachedValue is Newtonsoft.Json.Linq.JObject jObj)
                    {
                        jObj["fromCache"] = true;
                    }
                }

                return Ok(new SuccessDataResult<object>(new
                {
                    message = $"Cache aspect simulation test - {(isHit ? "HIT" : "MISS")}",
                    cacheKey,
                    isHit,
                    result = cachedValue,
                    entityDuration = _cacheAspectConfigurationService.GetDurationForEntity(request.EntityType),
                    timestamp = DateTime.UtcNow
                }));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache aspect simulation test failed: {ex.Message}"));
            }
        }
    }

    public class TestCacheRequest
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class TestObject
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsActive { get; set; }
    }

    public class AspectTestRequest
    {
        public string ClassName { get; set; } = "TestManager";
        public string MethodName { get; set; } = "GetById";
        public string ParameterHash { get; set; } = "hash123";
        public string EntityType { get; set; } = "Member";
        public int EntityId { get; set; } = 123;
    }
}
