version: '3.8'

services:
  redis:
    image: redis:7.2-alpine
    container_name: gymproject-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
    networks:
      - gymproject-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: gymproject-redis-insight
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - redisinsight_data:/db
    networks:
      - gymproject-network
    depends_on:
      - redis

volumes:
  redis_data:
    driver: local
  redisinsight_data:
    driver: local

networks:
  gymproject-network:
    driver: bridge
